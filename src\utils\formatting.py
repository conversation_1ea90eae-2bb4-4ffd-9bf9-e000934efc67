"""
Formatting utilities.

This module provides utilities for formatting various types of data.
"""

import logging

# Set up logging
logger = logging.getLogger(__name__)


def format_duration(milliseconds: float) -> str:
    """Converts milliseconds to minutes and seconds.

    Args:
        milliseconds: Duration in milliseconds

    Returns:
        str: Formatted duration string in the format "Xm Y.ZZs"
    """
    minutes, seconds = divmod(milliseconds / 1000, 60)
    return f"{int(minutes)}m {seconds:.2f}s"


def format_bytes(size_in_bytes: float) -> str:
    """Converts bytes to a human-readable format (MB or GB).

    Args:
        size_in_bytes: Size in bytes

    Returns:
        str: Human-readable size string
    """
    if size_in_bytes >= 1_073_741_824:  # 1 GB
        return f"{size_in_bytes / 1_073_741_824:.2f} GB"
    if size_in_bytes >= 1_048_576:  # 1 MB
        return f"{size_in_bytes / 1_048_576:.2f} MB"
    return f"{size_in_bytes} Bytes"
