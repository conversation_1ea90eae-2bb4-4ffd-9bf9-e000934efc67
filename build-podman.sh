#!/bin/bash

# <PERSON>ript to build a Podman image with GitLab repository access

# Default values
CI_PROJECT_ID=${CI_PROJECT_ID:-""}
CI_JOB_TOKEN=${CI_JOB_TOKEN:-""}
IMAGE_TAG=${IMAGE_TAG:-"latest"}
IMAGE_NAME=${IMAGE_NAME:-"kpiclick-app-dev"}

# Display help message
show_help() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  --project-id ID    GitLab project ID (required for private repo access)"
    echo "  --job-token TOKEN  GitLab CI job token (required for private repo access)"
    echo "  --tag TAG          Image tag (default: latest)"
    echo "  --name NAME        Image name (default: kpi_click)"
    echo "  -h, --help         Show this help message"
    exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    key="$1"
    case $key in
        --project-id)
            CI_PROJECT_ID="$2"
            shift 2
            ;;
        --job-token)
            CI_JOB_TOKEN="$2"
            shift 2
            ;;
        --tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        --name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            ;;
    esac
done

# Build the image
echo "Building Podman image: ${IMAGE_NAME}:${IMAGE_TAG}"
echo "Using GitLab project ID: ${CI_PROJECT_ID:-'Not provided'}"
echo "Using GitLab job token: ${CI_JOB_TOKEN:+'Provided (hidden)'}"

podman build \
    --build-arg CI_PROJECT_ID="$CI_PROJECT_ID" \
    --build-arg CI_JOB_TOKEN="$CI_JOB_TOKEN" \
    -t "${IMAGE_NAME}:${IMAGE_TAG}" .

echo "Build completed."
echo "To save the image: podman save ${IMAGE_NAME}:${IMAGE_TAG} -o ${IMAGE_NAME}.tar"
