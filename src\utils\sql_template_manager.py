"""
SQL template management utilities.

This module provides utilities for managing SQL templates.
"""

import os
import logging
import jinja2
from typing import Dict, Any, Optional, List, Union, Tuple, Callable
from pathlib import Path

from src.utils.sql_sanitizer import sanitize_sql, validate_sql, normalize_sql
from src.utils.sql_formatter import format_sql, format_jinja_template
from src.core.exceptions import QueryError

# Set up logging
logger = logging.getLogger(__name__)


class SQLTemplateManager:
    """
    Manager for SQL templates using Jinja2.

    This class provides methods for loading, rendering, and validating SQL templates.
    """

    def __init__(
        self,
        template_dir: str = "./src/queries/templates",
        custom_filters: Optional[Dict[str, Callable[..., Any]]] = None,
        auto_escape: bool = False,
        trim_blocks: bool = True,
        lstrip_blocks: bool = True,
    ):
        """
        Initialize the SQL template manager.

        Args:
            template_dir: Directory containing SQL templates
            custom_filters: Custom Jinja2 filters to register
            auto_escape: Whether to auto-escape variables in templates
            trim_blocks: Whether to trim blocks in templates
            lstrip_blocks: Whether to left-strip blocks in templates

        Raises:
            ValueError: If template_dir does not exist
        """
        self.template_dir = template_dir

        # Validate template directory
        if not os.path.exists(template_dir):
            raise ValueError(f"Template directory does not exist: {template_dir}")

        # Set up Jinja2 environment
        self.loader = jinja2.FileSystemLoader(searchpath=template_dir)
        self.env = jinja2.Environment(
            loader=self.loader,
            autoescape=auto_escape,
            trim_blocks=trim_blocks,
            lstrip_blocks=lstrip_blocks,
        )

        # Register default filters
        self._register_default_filters()

        # Register custom filters
        if custom_filters:
            for name, func in custom_filters.items():
                self.env.filters[name] = func

        # Cache for templates
        self.template_cache = {}

        logger.info(f"Initialized SQL template manager with directory: {template_dir}")

    def _register_default_filters(self):
        """Register default Jinja2 filters."""
        # SQL-specific filters
        self.env.filters["sanitize"] = lambda v: sanitize_sql(str(v))
        self.env.filters["normalize"] = lambda v: normalize_sql(str(v))

        # String manipulation filters
        self.env.filters["quote"] = lambda v: f"'{v}'"
        self.env.filters["double_quote"] = lambda v: f'"{v}"'
        self.env.filters["escape_quotes"] = lambda v: v.replace("'", "''")

    def get_template(self, template_name: str) -> jinja2.Template:
        """
        Get a template by name.

        Args:
            template_name: Name of the template

        Returns:
            Jinja2 template object

        Raises:
            QueryError: If template does not exist
        """
        # Check if template is in cache
        if template_name in self.template_cache:
            return self.template_cache[template_name]

        try:
            # Load template
            template = self.env.get_template(template_name)

            # Cache template
            self.template_cache[template_name] = template

            return template
        except jinja2.exceptions.TemplateNotFound:
            error_msg = f"Template not found: {template_name}"
            logger.error(error_msg)
            raise QueryError(error_msg)
        except jinja2.exceptions.TemplateSyntaxError as e:
            error_msg = f"Template syntax error in {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def render_template(
        self,
        template_name: str,
        context: Dict[str, Any],
        validate: bool = True,
        normalize: bool = False,
        dialect: str = "clickhouse",
    ) -> str:
        """
        Render a template with context.

        Args:
            template_name: Name of the template
            context: Context variables for template rendering
            validate: Whether to validate the rendered SQL
            normalize: Whether to normalize the rendered SQL
            dialect: SQL dialect to use for formatting

        Returns:
            Rendered SQL query

        Raises:
            QueryError: If template rendering or validation fails
        """
        try:
            # Get template
            template = self.get_template(template_name)

            # Render template
            rendered_sql = template.render(**context)

            # Validate SQL if requested
            if validate:
                is_valid, error_message = validate_sql(rendered_sql)
                if not is_valid:
                    error_msg = (
                        f"Invalid SQL in template {template_name}: {error_message}"
                    )
                    logger.error(error_msg)
                    raise QueryError(error_msg)

            # Normalize SQL if requested
            if normalize:
                # Use SQLFluff for formatting
                formatted_sql, success = format_sql(rendered_sql, dialect=dialect)
                if success:
                    logger.debug(
                        f"SQL query from template {template_name} formatted successfully with SQLFluff"
                    )
                    rendered_sql = formatted_sql
                else:
                    logger.warning(
                        f"SQLFluff formatting failed for template {template_name}, falling back to basic formatting"
                    )
                    rendered_sql = normalize_sql(rendered_sql)

            return rendered_sql

        except jinja2.exceptions.UndefinedError as e:
            error_msg = f"Undefined variable in template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)
        except Exception as e:
            error_msg = f"Error rendering template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def list_templates(self, extension: str = ".sql") -> List[str]:
        """
        List all templates in the template directory.

        Args:
            extension: File extension to filter by

        Returns:
            List of template names
        """
        return [t for t in self.env.list_templates() if t.endswith(extension)]

    def get_template_source(self, template_name: str) -> str:
        """
        Get the source code of a template.

        Args:
            template_name: Name of the template

        Returns:
            Template source code

        Raises:
            QueryError: If template does not exist
        """
        try:
            if self.env.loader is None:
                error_msg = "Jinja2 environment loader is not initialized."
                logger.error(error_msg)
                raise QueryError(error_msg)
            return self.env.loader.get_source(self.env, template_name)[0]
        except jinja2.exceptions.TemplateNotFound:
            error_msg = f"Template not found: {template_name}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def add_template(self, template_name: str, template_source: str) -> None:
        """
        Add a new template to the manager.

        Args:
            template_name: Name of the template
            template_source: Template source code

        Raises:
            QueryError: If template already exists or has syntax errors
        """
        # Check if template already exists
        template_path = os.path.join(self.template_dir, template_name)
        if os.path.exists(template_path):
            error_msg = f"Template already exists: {template_name}"
            logger.error(error_msg)
            raise QueryError(error_msg)

        try:
            # Validate template syntax
            self.env.from_string(template_source)

            # Write template to file
            with open(template_path, "w") as f:
                f.write(template_source)

            # Clear cache
            if template_name in self.template_cache:
                del self.template_cache[template_name]

            logger.info(f"Added template: {template_name}")

        except jinja2.exceptions.TemplateSyntaxError as e:
            error_msg = f"Template syntax error in {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)
        except Exception as e:
            error_msg = f"Error adding template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def update_template(self, template_name: str, template_source: str) -> None:
        """
        Update an existing template.

        Args:
            template_name: Name of the template
            template_source: Template source code

        Raises:
            QueryError: If template does not exist or has syntax errors
        """
        # Check if template exists
        template_path = os.path.join(self.template_dir, template_name)
        if not os.path.exists(template_path):
            error_msg = f"Template does not exist: {template_name}"
            logger.error(error_msg)
            raise QueryError(error_msg)

        try:
            # Validate template syntax
            self.env.from_string(template_source)

            # Write template to file
            with open(template_path, "w") as f:
                f.write(template_source)

            # Clear cache
            if template_name in self.template_cache:
                del self.template_cache[template_name]

            logger.info(f"Updated template: {template_name}")

        except jinja2.exceptions.TemplateSyntaxError as e:
            error_msg = f"Template syntax error in {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)
        except Exception as e:
            error_msg = f"Error updating template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def delete_template(self, template_name: str) -> None:
        """
        Delete a template.

        Args:
            template_name: Name of the template

        Raises:
            QueryError: If template does not exist
        """
        # Check if template exists
        template_path = os.path.join(self.template_dir, template_name)
        if not os.path.exists(template_path):
            error_msg = f"Template does not exist: {template_name}"
            logger.error(error_msg)
            raise QueryError(error_msg)

        try:
            # Delete template file
            os.remove(template_path)

            # Clear cache
            if template_name in self.template_cache:
                del self.template_cache[template_name]

            logger.info(f"Deleted template: {template_name}")

        except Exception as e:
            error_msg = f"Error deleting template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def format_template(
        self, template_name: str, dialect: str = "clickhouse"
    ) -> Tuple[str, bool]:
        """
        Format a template using SQLFluff.

        Args:
            template_name: Name of the template
            dialect: SQL dialect to use for formatting

        Returns:
            Tuple of (formatted template, success flag)

        Raises:
            QueryError: If template does not exist
        """
        try:
            # Get template source
            template_source = self.get_template_source(template_name)

            # Format template using SQLFluff
            formatted_template, success = format_jinja_template(
                template_source, dialect=dialect
            )

            if success:
                logger.info(f"Template {template_name} formatted successfully")
            else:
                logger.warning(f"Failed to format template {template_name}")

            return formatted_template, success

        except Exception as e:
            error_msg = f"Error formatting template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)

    def format_and_save_template(
        self, template_name: str, dialect: str = "clickhouse"
    ) -> bool:
        """
        Format a template using SQLFluff and save it.

        Args:
            template_name: Name of the template
            dialect: SQL dialect to use for formatting

        Returns:
            True if formatting and saving was successful, False otherwise

        Raises:
            QueryError: If template does not exist
        """
        try:
            # Format template
            formatted_template, success = self.format_template(template_name, dialect)

            if not success:
                logger.warning(f"Failed to format template {template_name}")
                return False

            # Update template
            self.update_template(template_name, formatted_template)

            logger.info(f"Template {template_name} formatted and saved successfully")
            return True

        except Exception as e:
            error_msg = f"Error formatting and saving template {template_name}: {e}"
            logger.error(error_msg)
            raise QueryError(error_msg)
