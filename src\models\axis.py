from typing import Dict, List, Optional, Any, Tuple, Union, Literal
from pydantic import BaseModel, Field


class DictLikeModel(BaseModel):
    """Base model with dictionary-like access methods."""

    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-like access to model attributes."""
        return getattr(self, key, None)

    def get(self, key: str, default: Any = None) -> Any:
        """Dictionary-like get method with default value."""
        return getattr(self, key, default)

    def keys(self) -> List[str]:
        """Return a list of attribute names."""
        return list(self.__class__.model_fields.keys())

    def items(self) -> List[Tuple[str, Any]]:
        """Return a list of (key, value) pairs."""
        return [(k, getattr(self, k, None)) for k in self.keys()]


class AxisLabel(DictLikeModel):
    """Model for axis label data."""

    position_number: int
    value_group_name: Optional[str] = None


class AxisData(DictLikeModel):
    """Model for axis data."""

    axis_id: Optional[int] = None
    axis_positions: Optional[List[int]] = Field(default_factory=list)
    database: Optional[str] = None
    type: Optional[str] = None
    name: Optional[str] = None
    full_name: Optional[str] = None
    labels: Optional[List[Dict[str, Any]]] = Field(default_factory=list)
    ddl: Dict[str, Any] = Field(default_factory=lambda: {"cte": "", "queries": []})


class FilterData(DictLikeModel):
    """Model for filter data."""

    database: Optional[str] = None
    type: Optional[str] = None
    name: Optional[str] = None
    full_name: Optional[str] = None
    ddl: Dict[str, Any] = Field(default_factory=lambda: {"cte": "", "queries": []})


class FactsData(DictLikeModel):
    """Model for facts data."""

    id: int
    relative_axis: Optional[str] = None
    relative_position: Optional[int] = None
    code_name: Optional[str] = None
    display_name: Optional[str] = None
    required_facts: Optional[Union[str, List[str]]] = None
    formula: Optional[str] = None
    divisor: Optional[int] = None


class Period(DictLikeModel):
    """Model for period data."""

    label: str
    date_start: str
    date_end: str
    total_positions: Optional[int] = None
    split_axis: Optional[str] = None
    threshold: Optional[int] = None


class SUFactData(DictLikeModel):
    """Model for SU (Standard Unit) fact data."""

    su_name: str
    measure: str
    dialect: Literal["clickhouse", "postgres"] = "clickhouse"
    formula: str
