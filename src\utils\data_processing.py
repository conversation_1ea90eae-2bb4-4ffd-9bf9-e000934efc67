"""
Utility functions for data processing.

This module provides utility functions for common data processing operations
used across the application, particularly for handling axis and filter data.
"""

import asyncio
import logging
import math
from typing import Any, Dict, Optional, Callable, Coroutine, List, Tuple

from src.models.axis import AxisData, FilterData

logger = logging.getLogger(__name__)


def extract_full_name(value: Any, object_type: str) -> Optional[str]:
    """
    Extract the full name from a value that could be a string, model, or dictionary.

    Args:
        value: The value to extract the name from (string, AxisData/FilterData, or dictionary)
        object_type: Type of object ('axis' or 'filter')

    Returns:
        The extracted full name or None if it couldn't be determined
    """
    if not value:
        return None

    # If it's already a string, use it directly
    if isinstance(value, str):
        return value

    # If it's a model object (AxisData or FilterData)
    if isinstance(value, (AxisData, FilterData)):
        # If it has a full_name, use it
        if value.full_name:
            return value.full_name
        # Otherwise construct it from components if available
        elif value.database and value.type and value.name:
            return f"{value.database}.{value.type}_{value.name}"
        # For filters, sometimes only name is available
        elif object_type == "filter" and value.name:
            return value.name

    # If it's a dictionary
    elif isinstance(value, dict):
        # If it has a full_name, use it
        if value.get("full_name"):
            return value["full_name"]
        # Otherwise construct it from components if available
        elif all(k in value for k in ["database", "type", "name"]):
            return f"{value['database']}.{value['type']}_{value['name']}"
        # For filters, sometimes only name is available
        elif object_type == "filter" and value.get("name"):
            return value["name"]

    # If we couldn't determine a valid name
    return None


def escape_sql_string(value: str) -> str:
    """
    Escape single quotes in a string for SQL queries.

    Args:
        value: The string to escape

    Returns:
        The escaped string with single quotes doubled
    """
    if value is None:
        return None
    return value.replace("'", "''")


def run_async_with_event_loop(coroutine: Coroutine) -> Any:
    """
    Run an async coroutine with proper event loop handling.

    This function handles the complexities of running an async coroutine
    in different event loop contexts.

    Args:
        coroutine: The coroutine to run

    Returns:
        The result of the coroutine
    """
    # Make sure we're dealing with a coroutine object
    if not asyncio.iscoroutine(coroutine):
        logger.warning(f"Expected a coroutine object, got {type(coroutine)}")
        return coroutine  # Return as is if not a coroutine

    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an event loop, we can't block
            logger.warning(
                "Event loop already running, creating a new one for synchronous execution"
            )
            # Create a new event loop for this thread
            new_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(new_loop)
            try:
                # Run the coroutine in the new loop
                return new_loop.run_until_complete(coroutine)
            finally:
                # Clean up
                new_loop.close()
                asyncio.set_event_loop(loop)
        else:
            # If we have a loop but it's not running, use it
            logger.info("Using existing event loop")
            return loop.run_until_complete(coroutine)
    except RuntimeError:
        # If there's no event loop, create a new one
        logger.info("Creating new event loop")
        return asyncio.run(coroutine)
    except Exception as e:
        logger.error(f"Error running coroutine: {e}", exc_info=True)
        # Return a default empty result to avoid None errors
        return {}


def run_async_in_background(
    coroutine: Coroutine, callback: Optional[Callable] = None
) -> None:
    """
    Run an async coroutine in the background of an existing event loop.

    This function is useful when you want to start a coroutine but don't
    need to wait for its result.

    Args:
        coroutine: The coroutine to run
        callback: Optional callback function to call with the result
    """
    try:
        # Try to get the current event loop
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # If we're already in an event loop, run the coroutine in the background
            future = asyncio.run_coroutine_threadsafe(coroutine, loop)
            if callback:
                future.add_done_callback(callback)
        else:
            # If we have a loop but it's not running, run the coroutine
            result = loop.run_until_complete(coroutine)
            if callback:
                callback(result)
    except RuntimeError:
        # If there's no event loop, create a new one
        result = asyncio.run(coroutine)
        if callback:
            callback(result)


def calculate_split_parameters(
    period: tuple, axes_data: Dict[str, Any], logger: logging.Logger
) -> tuple:
    """
    Calculate query splitting parameters.

    Args:
        period: Tuple of period name, start date, end date
        axes_data: Dictionary of axis data
        logger: Logger instance

    Returns:
        tuple: (total_positions, split_axis, threshold)
    """
    from datetime import datetime
    from dateutil import relativedelta

    start, end = period[1], period[2]
    delta = relativedelta.relativedelta(
        datetime.strptime(end, "%Y-%m-%d"), datetime.strptime(start, "%Y-%m-%d")
    )
    period_length = delta.years * 12 + delta.months + 1

    total_positions = 1
    for axis_data in axes_data.values():
        axis_positions = axis_data.axis_positions or []
        if axis_positions and len(axis_positions) > 0:
            total_positions *= axis_positions[0]

    logger.info(f"Total positions: {total_positions}")

    split_axis = None
    threshold = 0

    # Handle fourth_axis
    if "fourth_axis" in axes_data:
        fourth_axis = axes_data["fourth_axis"]
        fourth_axis_positions = fourth_axis.axis_positions or []

        if fourth_axis_positions and len(fourth_axis_positions) > 0:
            threshold = int(3_000_000 / period_length)
            split_axis = "fourth_axis" if total_positions > threshold else None

    # Handle first_axis
    # if not split_axis and "first_axis" in axes_data:
    #     first_axis = axes_data["first_axis"]
    #     first_axis_positions = first_axis.axis_positions or []

    #     if (
    #         first_axis_positions
    #         and len(first_axis_positions) > 0
    #         and first_axis_positions[0] >= 200
    #     ):
    #         threshold = int(100_000_000 / period_length)
    #         split_axis = "first_axis" if total_positions > threshold else None

    return (total_positions, split_axis, threshold)


def calculate_ddl_chunk_indices(
    total_positions: int, axis_positions: int, threshold: int, total_ddls: int
) -> List[Tuple[int, int]]:
    """
    Calculate DDL index chunks for axis splitting based on threshold.

    This function encapsulates the chunking math logic for splitting DDL queries
    into manageable chunks based on position thresholds.

    Args:
        total_positions: Total positions across all axes
        axis_positions: Number of positions in the axis being split
        threshold: Maximum positions per chunk
        total_ddls: Total number of DDL queries to split

    Returns:
        List of (start_index, end_index) tuples representing chunk boundaries

    Raises:
        ValueError: If parameters are invalid
    """
    if total_positions <= 0:
        raise ValueError("total_positions must be positive")
    if axis_positions <= 0:
        raise ValueError("axis_positions must be positive")
    if threshold <= 0:
        raise ValueError("threshold must be positive")
    if total_ddls <= 0:
        raise ValueError("total_ddls must be positive")

    # Calculate how many axis positions we can include per chunk
    other_axes_positions_product = total_positions // axis_positions
    max_axis_positions_per_chunk = threshold // other_axes_positions_product
    max_axis_positions_per_chunk = max(1, max_axis_positions_per_chunk)  # At least 1

    # Each DDL query represents one position in the axis
    ddls_per_chunk = max_axis_positions_per_chunk
    num_chunks = math.ceil(total_ddls / ddls_per_chunk)

    # Generate chunk indices
    chunk_indices = []
    for chunk_idx in range(num_chunks):
        start_idx = chunk_idx * ddls_per_chunk
        end_idx = min(start_idx + ddls_per_chunk, total_ddls)
        chunk_indices.append((start_idx, end_idx))

    return chunk_indices
