"""
Error handling utilities.

This module provides utilities for handling errors in the application.
"""

import logging
import sys
import traceback
from typing import Dict, List, Any, Optional, Callable, Type, Union, TypeVar

from src.core.exceptions import KPIError

# Type variable for generic function return type
T = TypeVar("T")


def format_exception(exc: Exception) -> str:
    """
    Format an exception with traceback for logging.

    Args:
        exc: The exception to format

    Returns:
        Formatted exception string with traceback
    """
    return "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))


def log_exception(
    logger: logging.Logger,
    exc: Exception,
    message: str = "An error occurred",
    level: int = logging.ERROR,
) -> None:
    """
    Log an exception with traceback.

    Args:
        logger: Logger to use
        exc: Exception to log
        message: Message to log with the exception
        level: Logging level to use
    """
    exc_info = sys.exc_info()
    logger.log(level, f"{message}: {str(exc)}", exc_info=exc_info)


def handle_exceptions(
    exceptions: List[Type[Exception]],
    handler: Callable[[Exception], Any],
    reraise: bool = False,
    logger: Optional[logging.Logger] = None,
    log_message: str = "An error occurred",
) -> Callable:
    """
    Decorator to handle specific exceptions.

    Args:
        exceptions: List of exception types to catch
        handler: Function to handle the exceptions
        reraise: Whether to reraise the exception after handling
        logger: Logger to use for logging the exception
        log_message: Message to log with the exception

    Returns:
        Decorated function
    """

    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except tuple(exceptions) as e:
                if logger:
                    log_exception(logger, e, log_message)
                result = handler(e)
                if reraise:
                    raise
                return result

        return wrapper

    return decorator


def safe_execute(
    func: Callable[..., T],
    *args,
    default: Optional[T] = None,
    exceptions: List[Type[Exception]] = None,
    logger: Optional[logging.Logger] = None,
    log_message: str = "Error executing function",
    **kwargs,
) -> T:
    """
    Execute a function safely, catching exceptions.

    Args:
        func: Function to execute
        *args: Arguments to pass to the function
        default: Default value to return if an exception occurs
        exceptions: List of exception types to catch (defaults to Exception)
        logger: Logger to use for logging the exception
        log_message: Message to log with the exception
        **kwargs: Keyword arguments to pass to the function

    Returns:
        Result of the function or default value if an exception occurs
    """
    if exceptions is None:
        exceptions = [Exception]

    try:
        return func(*args, **kwargs)
    except tuple(exceptions) as e:
        if logger:
            log_exception(logger, e, log_message)
        return default


def validate_input(
    value: Any,
    validator: Callable[[Any], bool],
    error_message: str = "Invalid input",
    exception_type: Type[Exception] = ValueError,
) -> None:
    """
    Validate input value using a validator function.

    Args:
        value: Value to validate
        validator: Function that returns True if value is valid
        error_message: Error message to use if validation fails
        exception_type: Exception type to raise if validation fails

    Raises:
        exception_type: If validation fails
    """
    if not validator(value):
        raise exception_type(error_message)


def collect_errors(
    func: Callable[..., Dict[str, Any]], error_key: str = "errors", *args, **kwargs
) -> Dict[str, Any]:
    """
    Collect errors from a function that returns a dictionary with an errors key.

    Args:
        func: Function to execute
        error_key: Key in the result dictionary that contains errors
        *args: Arguments to pass to the function
        **kwargs: Keyword arguments to pass to the function

    Returns:
        Dictionary with the function result and any errors
    """
    # Initialize a fresh result dictionary for each call
    result = {error_key: []}

    try:
        # Execute the function and get its result
        func_result = func(*args, **kwargs)

        # Update the result with the function result
        result.update(func_result)

        # If the function result has errors, add them to our result
        # but don't duplicate them
        if error_key in func_result and error_key in result:
            # Create a new list to avoid modifying the original
            result[error_key] = list(func_result[error_key])

    except Exception as e:
        # If an exception occurs, add it to the errors
        result[error_key].append(str(e))

    return result
