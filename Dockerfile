FROM python:3.12-slim
LABEL org.opencontainers.image.authors="<EMAIL>"

# Dependencies installation
RUN python -m pip install poetry

# set application path
WORKDIR /kpi_click

RUN poetry config virtualenvs.in-project true

# Configure Poetry to use GitLab repository
# These ARGs can be passed during build time
ARG CI_PROJECT_ID
ARG CI_JOB_TOKEN

# Configure Poetry to use GitLab repository if tokens are provided
RUN if [ -n "$CI_PROJECT_ID" ] && [ -n "$CI_JOB_TOKEN" ]; then \
    poetry config repositories.gitlab "https://gitlab.icmr.ru/dev/$CI_PROJECT_ID" && \
    poetry config http-basic.gitlab gitlab-ci-token "$CI_JOB_TOKEN"; \
    fi

# copy the poetry dep info to the image
COPY poetry.lock pyproject.toml .
RUN poetry install --only=main --no-interaction --no-ansi

# copy the rest of the
COPY . .

