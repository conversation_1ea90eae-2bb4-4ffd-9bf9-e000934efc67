"""
Job API client for interacting with the job status API.

This module provides functions to fetch job status and update job information
using the job status API endpoint.
"""

import logging
import json
import asyncio
from typing import Dict, Any, Optional, Literal, Union
import httpx
from dotenv import load_dotenv
import os

# Set up logging
logger = logging.getLogger(__name__)

# Import config
from src.core.config import config

# Get API URL from config
JOB_API_URL = config.job_api_url

# Define job status types
JobStatus = Literal["wait", "inprogress", "done", "error", "canceled", "pending"]


async def get_job_status_by_id(job_id: int) -> Dict[str, Any]:
    """
    Get job status and request information by job ID.

    Args:
        job_id: The job ID to fetch

    Returns:
        Dictionary containing job status and request information

    Raises:
        Exception: If the API request fails
    """
    url = f"{JOB_API_URL}/v1/job/status/{job_id}"

    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url)
            response.raise_for_status()
            result = response.json()
            logger.info(f"Successfully retrieved job status for job_id={job_id}")
            return result
    except Exception as e:
        logger.error(f"Error fetching job status for job_id={job_id}: {e}")
        raise


def get_job_request(job_id: int) -> Dict[str, Any]:
    """
    Get job request information by job ID (synchronous wrapper).

    Args:
        job_id: The job ID to fetch

    Returns:
        Dictionary containing job request information with additional metadata:
        - user_name: Username who initiated the job
        - dt_job_start: Job start timestamp
        - status: Current job status
        - type: Job type

    Raises:
        Exception: If the API request fails
    """
    try:
        # Create a new event loop or use the existing one
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
        except RuntimeError:
            # If there's no event loop, create a new one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Run the async function and get the result
        job_status = loop.run_until_complete(get_job_status_by_id(job_id))

        # Extract metadata from job status
        metadata = {
            "user_name": job_status.get("user_name", ""),
            "dt_job_start": job_status.get("dt_job_start", ""),
            "status": job_status.get("status", ""),
            "type": job_status.get("type", ""),
        }

        # Log the metadata
        logger.info(
            f"Job {job_id} metadata: user={metadata['user_name']}, started={metadata['dt_job_start']}, status={metadata['status']}"
        )

        # Extract the request field from the job status
        if "request" in job_status:
            # If request is a string, parse it as JSON
            if isinstance(job_status["request"], str):
                try:
                    request_data = json.loads(job_status["request"])
                    # Add metadata to the request data
                    request_data.update({"_metadata": metadata})
                    return request_data
                except json.JSONDecodeError:
                    logger.error(f"Invalid JSON in job request for job_id={job_id}")
                    return {"_metadata": metadata}
            # If request is already a dictionary, return it
            elif isinstance(job_status["request"], dict):
                request_data = job_status["request"]
                # Add metadata to the request data
                request_data.update({"_metadata": metadata})
                return request_data

        logger.error(f"No request field found in job status for job_id={job_id}")
        return {"_metadata": metadata}
    except Exception as e:
        logger.error(f"Error getting job request for job_id={job_id}: {e}")
        raise


async def async_set_job_status(
    job_id: int, status: JobStatus, error_message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update job status by job ID using the /v1/job/status endpoint.

    Args:
        job_id: The job ID to update
        status: The new status ("wait", "inprogress", "done", "error", "canceled", or "pending")
        error_message: Optional error message (required when status is "error")

    Returns:
        Dictionary containing the API response

    Raises:
        httpx.HTTPStatusError: If the API returns an HTTP error status code
        httpx.RequestError: If there's a network-related error
        Exception: If any other error occurs during the API request
    """
    # Base URL for the job status API - use the correct endpoint without '_put' suffix
    url = f"{JOB_API_URL}/v1/job/status"

    # Prepare URL parameters with the correct parameter name 'id_job' instead of 'job_id'
    params = {
        "id_job": job_id,  # Use 'id_job' as the API expects
        "status": status,
    }

    # Add error message if provided
    if error_message and status == "error":
        params["error_message"] = error_message
    elif status == "error" and not error_message:
        error_message = "Unknown error occurred"
        params["error_message"] = error_message

    try:
        async with httpx.AsyncClient() as client:
            # Use params for the request with PUT method as required by the API
            response = await client.put(url, params=params)
            response.raise_for_status()
            result = response.json()
            logger.info(
                f"Successfully updated job status for job_id={job_id} to {status}"
            )
            return result
    except httpx.HTTPStatusError as e:
        # Handle HTTP status errors specifically (e.g., 404, 500)
        logger.error(
            f"HTTP error updating job status for job_id={job_id} to {status}: {e.response.status_code} - {e.response.reason_phrase}"
        )
        logger.error(f"Request URL: {e.request.url}")
        try:
            # Try to get response content for more details
            error_detail = e.response.json()
            logger.error(f"Error details: {error_detail}")
        except Exception:
            # If response is not JSON, log the text content
            logger.error(f"Response content: {e.response.text}")
        raise
    except httpx.RequestError as e:
        # Handle request errors (connection issues, timeouts, etc.)
        logger.error(
            f"Request error updating job status for job_id={job_id} to {status}: {e}"
        )
        raise
    except Exception as e:
        # Handle any other unexpected errors
        logger.error(f"Error updating job status for job_id={job_id} to {status}: {e}")
        raise


def set_job_status(
    job_id: int, status: JobStatus, error_message: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update job status by job ID (synchronous wrapper) using the /v1/job/status endpoint.

    Args:
        job_id: The job ID to update
        status: The new status ("wait", "inprogress", "done", "error", "canceled", or "pending")
        error_message: Optional error message (required when status is "error")

    Returns:
        Dictionary containing the API response

    Raises:
        httpx.HTTPStatusError: If the API returns an HTTP error status code
        httpx.RequestError: If there's a network-related error
        Exception: If any other error occurs during the API request
    """
    try:
        # Create a new event loop or use the existing one
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
        except RuntimeError:
            # If there's no event loop, create a new one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Run the async function and get the result
        result = loop.run_until_complete(
            async_set_job_status(job_id, status, error_message)
        )
        return result
    except httpx.HTTPStatusError as e:
        # Pass through HTTP status errors from the async function
        logger.error(f"HTTP error in set_job_status for job_id={job_id}: {e}")
        raise
    except httpx.RequestError as e:
        # Pass through request errors from the async function
        logger.error(f"Request error in set_job_status for job_id={job_id}: {e}")
        raise
    except Exception as e:
        # Handle any other unexpected errors
        logger.error(f"Error setting job status for job_id={job_id} to {status}: {e}")
        raise


async def async_set_job_progress_message(job_id: int, message: str) -> Dict[str, Any]:
    """
    Set job progress message by job ID.

    Args:
        job_id: The job ID to update
        message: The progress message to set

    Returns:
        Dictionary containing the API response

    Raises:
        Exception: If the API request fails
    """
    url = f"{JOB_API_URL}/v1/job/progress_message"

    # Prepare parameters
    params = {
        "id_job": job_id,
        "message": message,
    }

    try:
        async with httpx.AsyncClient() as client:
            response = await client.put(url, params=params)
            response.raise_for_status()
            result = response.json()
            logger.debug(f"Set progress message for job_id={job_id}: {message}")
            return result
    except Exception as e:
        logger.warning(f"Error setting progress message for job_id={job_id}: {e}")
        # Don't raise the exception to avoid interrupting the main workflow
        return {"error": str(e)}


def set_job_progress_message(job_id: int, message: str) -> Dict[str, Any]:
    """
    Set job progress message by job ID (synchronous wrapper).

    Args:
        job_id: The job ID to update
        message: The progress message to set

    Returns:
        Dictionary containing the API response

    Raises:
        Exception: If the API request fails
    """
    try:
        # Create a new event loop or use the existing one
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
        except RuntimeError:
            # If there's no event loop, create a new one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Run the async function and get the result
        result = loop.run_until_complete(
            async_set_job_progress_message(job_id, message)
        )
        return result
    except Exception as e:
        logger.warning(f"Error setting progress message for job_id={job_id}: {e}")
        # Don't raise the exception to avoid interrupting the main workflow
        return {"error": str(e)}
