"""
Query service for the application.

This module provides a service for building and executing SQL queries.
It encapsulates the functionality of the ClickHouseQuery and QueryProcessor classes.
"""

from typing import Dict, Optional, Tuple, Callable, Any

from src.core.connection import ClickHouseConnection
from src.core.connection_manager import connection_manager
from src.core.result_store import ResultStore
from src.models.axis import Period
from src.models.kpi import KPIType, ValidationResult
from src.queries.query_builder import ClickHouseQuery
from src.processors.query_processor import QueryProcessor
from src.services.base_service import BaseService


class QueryService(BaseService):
    """
    Service for building and executing SQL queries.

    This service encapsulates the functionality of the ClickHouseQuery and
    QueryProcessor classes and provides a more service-oriented interface.
    """

    def __init__(
        self,
        connection: Optional[ClickHouseConnection] = None,
        msg_logger_func: Optional[Callable] = None,
    ):
        """
        Initialize the query service.

        Args:
            connection: ClickHouse connection
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)

        # Get the connection from the connection manager if not provided
        self.connection = connection or connection_manager.get_clickhouse_connection()

        # Create the result store
        self.result_store = ResultStore(self.connection)

    def create_query_builder(
        self,
        validation_result: ValidationResult,
    ) -> ClickHouseQuery:
        """
        Create a query builder with the validated data from ValidationResult model.

        Args:
            validation_result: ValidationResult model containing all validated data

        Returns:
            Configured ClickHouseQuery instance
        """
        self.log_message("Creating query builder with validated data")

        # Log the types of data being passed to the query builder
        self.logger.info(
            f"Filters count: {len(validation_result.filters)}, Axes count: {len(validation_result.axes)}"
        )
        if validation_result.facts_data:
            self.logger.info(f"Facts data count: {len(validation_result.facts_data)}")
        if validation_result.required_facts:
            self.logger.info(
                f"Required facts count: {len(validation_result.required_facts)}"
            )

        # Get su_fact formula from su_fact_data if available
        su_fact = None
        if validation_result.su_fact_data:
            # Extract the formula directly from the SUFactData model
            su_fact = validation_result.su_fact_data.formula

        # Get the first period from the periods list
        if not validation_result.periods:
            raise ValueError(
                "At least one period is required to create a query builder"
            )

        period = validation_result.periods[0]

        return ClickHouseQuery(
            period=period,
            filters=validation_result.filters,
            axes=validation_result.axes,
            id_panel=validation_result.id_panel or 1,
            su_fact=validation_result.su_fact_data,
            facts_axis=validation_result.facts_data,
            required_facts=validation_result.required_facts,
            template_dir="./src/queries/templates_v1",
        )

    def create_query_processor(self, query_builder: ClickHouseQuery) -> QueryProcessor:
        """
        Create a query processor with the specified query builder.

        Args:
            query_builder: ClickHouseQuery instance

        Returns:
            Configured QueryProcessor instance
        """
        self.log_message("Creating query processor")

        return QueryProcessor(self.connection, query_builder, self.result_store)

    def process_query(
        self,
        query_processor: QueryProcessor,
        period: Period,
        kpi_type: KPIType = KPIType.STANDARD_KPI,
        job_id: str = "unknown",
        analysis_name: str = "kpi_analysis",
        id_panel: int = 1,
        combined_result_id: Optional[str] = None,
        username: str = "",
        su_fact_name: Optional[str] = None,
    ) -> Tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Process a query and store the results.

        Args:
            query_processor: QueryProcessor instance
            period: Period model containing start and end dates
            kpi_type: Type of KPI to process
            job_id: Job ID for tracking
            analysis_name: Analysis name
            id_panel: Panel ID
            combined_result_id: Optional ID for combining multiple periods into one table
            username: Username for the job
            su_fact_name: Name of the SU fact used in the query

        Returns:
            Tuple of (Result ID if successful or None, Error info if failed or None)
        """
        self.log_message(f"Processing query for period {period.label}")

        return query_processor.process_query(
            period=period,
            kpi_type=kpi_type,
            job_id=job_id,
            analysis_name=analysis_name,
            id_panel=id_panel,
            combined_result_id=combined_result_id,
            username=username,
            su_fact_name=su_fact_name,
        )
