from enum import Enum
from typing import Dict, List, Optional, Union
from pydantic import Field, BaseModel

from src.models.axis import (
    DictLikeModel,
    AxisData,
    FilterData,
    Period,
    FactsData,
    SUFactData,
)


class ValidationResult(BaseModel):
    """Model for validation result data."""

    axes: Dict[str, AxisData] = Field(default_factory=dict)
    filters: Dict[str, FilterData] = Field(default_factory=dict)
    periods: List[Period] = Field(default_factory=list)
    facts_data: List[FactsData] = Field(default_factory=list)
    required_facts: List[str] = Field(default_factory=list)
    su_fact_data: Optional[SUFactData] = None
    id_panel: Optional[int] = 1


class KPIType(str, Enum):
    """Enum for KPI types."""

    STANDARD_KPI = "standard_kpi"
    CATMAN_KPI = "catman_kpi"


class KPIRequest(DictLikeModel):
    """Model for KPI request data."""

    periods: List[Dict]
    axes: Dict
    filters: Dict
    kpi_type: KPIType
    id_panel: int
    analysis_name: str
    facts_axis: Optional[List[Dict]] = None
    su_fact: Optional[str] = None
    required_facts: Optional[List[str]] = None


class KPIError(DictLikeModel):
    """Model for detailed KPI error information."""

    message: str
    error_code: Optional[int] = None
    period: Optional[str] = None


class KPIResult(DictLikeModel):
    """Model for KPI processing result."""

    exported_tables: List[str] = Field(default_factory=list)
    errors: List[Union[str, KPIError]] = Field(default_factory=list)
    result_ids: List[str] = Field(default_factory=list)

    def add_error(
        self,
        error: Union[str, Exception],
        error_code: Optional[int] = None,
        period: Optional[str] = None,
    ) -> None:
        """
        Add an error to the result with simplified information.

        Args:
            error: Error message or exception
            error_code: Optional error code (e.g., ClickHouse error code)
            period: Optional period name where the error occurred
        """
        if isinstance(error, Exception):
            message = str(error)
        else:
            message = error

        self.errors.append(
            KPIError(
                message=message,
                error_code=error_code,
                period=period,
            )
        )
