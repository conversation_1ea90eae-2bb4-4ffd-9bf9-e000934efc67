{# Calculation of buyers #}
SELECT
    hhkey,
    rwbasis,
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {% if axis_data["type"] != "axsh" %}
                {{ axis_key }}_position_number,
            {% endif %}
        {% endif %}
    {% endfor %}
    {% for fact in required_facts %}
        {% if fact == 'weight_wave' %}
            any({{ fact }}) AS buyers_ww,
        {% endif %}
        {% if fact == 'BUF' %}
            count(*) as trips_raw,
            sum(weight_wave) AS trips_ww,
            any(population) AS population,
        {% endif %}
        {% if fact == "rw_compensat" %}
            sum(rw_compensat * fullmass) AS trips_fullmass,
        {% endif %}
        {% if fact in ("volume_rp", "value_rp", "number_rp", "volume_rp_promo", "value_rp_promo", "number_rp_promo") %}
            sum({{ fact }}) AS {{ fact }},
        {% endif %}
    {% endfor %}
    {% if facts_axis|selectattr("code_name", "equalto", "loyalty_volume")|list %}
        any(volume_loyalty_base) AS volume_loyalty_base,
    {% endif %}
    {% if facts_axis|selectattr("code_name", "equalto", "loyalty_value")|list %}
        any(value_loyalty_base) AS value_loyalty_base,
    {% endif %}
    any(projectc) as projectc
FROM axis
{% if facts_axis|selectattr("code_name", "in", ["loyalty_value", "loyalty_volume"])|list %}
    LEFT JOIN (
    SELECT
    {% for fact in facts_axis %}
        {% if fact["code_name"] == "loyalty_volume" or fact["code_name"] == "loyalty_value" %}
            sum({{ fact["code_name"][8:] }}_rp) AS {{ fact["code_name"][8:] }}_loyalty_base,
        {% endif %}
    {% endfor %}
    hhkey
    FROM axis
    {% for fact in facts_axis %}
        {% if fact["code_name"] == "loyalty_volume" or fact["code_name"] == "loyalty_value" %}
            WHERE 
            {% if fact["relative_axis"] is not none %} 
                {{ fact["relative_axis"] }}_position_number = '{{ fact["relative_position"] }}|' 
            {% else %}
                first_axis_position_number = '1|'
            {% endif %}
        {% endif %}
    {% endfor %}
    GROUP BY hhkey
    ) AS loyalty_base USING (hhkey)
{% endif %}
GROUP BY hhkey, rwbasis
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] != "axsh" %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endif %}
{% endfor %}
