# PowerShell script to build a Podman image with GitLab repository access

param (
    [string]$ProjectId = "mg_client.git",
    [string]$JobToken = "**************************",
    [string]$Tag = "latest",
    [string]$Name = "kpiclick-app",
    [switch]$Help
)

# Display help message
function Show-Help {
    Write-Host "Usage: .\build-podman.ps1 [options]"
    Write-Host "Options:"
    Write-Host "  -ProjectId ID    GitLab project ID (required for private repo access)"
    Write-Host "  -JobToken TOKEN  GitLab CI job token (required for private repo access)"
    Write-Host "  -Tag TAG         Image tag (default: latest)"
    Write-Host "  -Name NAME       Image name (default: kpi_click)"
    Write-Host "  -Help            Show this help message"
    exit 0
}

if ($Help) {
    Show-Help
}

# Build the image
Write-Host "Building Podman image: ${Name}:${Tag}"
Write-Host "Using GitLab project ID: $(if ($ProjectId) { $ProjectId } else { 'Not provided' })"
Write-Host "Using GitLab job token: $(if ($JobToken) { 'Provided (hidden)' } else { 'Not provided' })"

podman build `
    --build-arg CI_PROJECT_ID="$ProjectId" `
    --build-arg CI_JOB_TOKEN="$JobToken" `
    -t "${Name}:${Tag}" .

Write-Host "Build completed."
Write-Host "To save the image: podman save ${Name}:${Tag} -o ${Name}.tar"
