"""
Error handling utilities for the KPI application.

This module provides centralized error handling functions to ensure
consistent error processing, reporting, and storage across the application.
"""

import logging
from typing import Any, Dict, Optional, Tuple, Union, List

from src.core.exceptions import QueryError
from src.utils.clickhouse_error_parser import parse_clickhouse_error, get_simplified_error_message


def handle_query_error(
    e: Exception, 
    context_message: str, 
    logger: logging.Logger
) -> QueryError:
    """
    Centralized error handling for query operations.
    
    Args:
        e: The original exception
        context_message: A message providing context for the error
        logger: Logger instance
        
    Returns:
        A properly formatted QueryError
    """
    error_msg = f"{context_message}: {e}"
    logger.error(error_msg, exc_info=True)
    
    # Check if it's already a QueryError
    if isinstance(e, QueryError):
        return e
        
    # Check if it's a ClickHouse error
    error_info = parse_clickhouse_error(str(e))
    if error_info["error_code"] is not None:
        query_error = QueryError(error_msg)
        query_error.error_code = error_info["error_code"]
        query_error.error_type = error_info["error_type"]
        return query_error
    
    # Regular error
    return QueryError(error_msg)


def report_error(
    error: Exception,
    job_id: str,
    period: Union[Tuple[str, str, str], str],
    result_store: Any,
    logger: logging.Logger,
    query_steps: Optional[List[str]] = None,
    query_ids: Optional[List[str]] = None,
    job_duration_ms: Optional[float] = None,
    **context
) -> None:
    """
    Report an error to the result store with consistent formatting.
    
    Args:
        error: The exception that occurred
        job_id: The ID of the job
        period: The period being processed
        result_store: The result store instance
        logger: Logger instance
        query_steps: Optional list of query steps executed before the error
        query_ids: Optional list of query IDs used
        job_duration_ms: Optional job duration in milliseconds
        **context: Additional context information for result storage
    """
    # Extract error details
    if isinstance(error, QueryError):
        error_code = getattr(error, "error_code", None)
        error_type = getattr(error, "error_type", None)
    else:
        error_info = parse_clickhouse_error(str(error))
        error_code = error_info["error_code"]
        error_type = error_info["error_type"]
    
    # Create simplified message
    simplified_message = get_simplified_error_message(str(error), error_code)
    
    # Create error info
    error_info = {
        "message": simplified_message,
        "error_code": error_code,
        "period": period[0] if isinstance(period, tuple) else period,
    }
    
    # Add error type if available
    if error_type:
        error_info["error_type"] = error_type
    
    # Store the error
    try:
        result_store.store_result_direct(
            query_text="SELECT 'Error' AS error",  # Dummy query
            job_id=job_id,
            period=period,
            query_steps="\n\n".join(query_steps) if query_steps else "",
            query_ids=query_ids,
            job_duration=job_duration_ms,
            error_info=error_info,
            **context
        )
        logger.info(f"Error information stored in ClickHouse for job {job_id}")
    except Exception as store_error:
        logger.error(f"Failed to store error information: {store_error}")


class TemporaryTable:
    """
    Context manager for temporary tables to ensure proper cleanup.
    
    Usage:
        with TemporaryTable(connection, processor, "table_name") as temp_table:
            # Use temp_table.name
            # Table will be automatically cleaned up when exiting the block
    """
    
    def __init__(
        self, 
        connection: Any, 
        processor: Any, 
        table_name: str, 
        query: str, 
        engine: str = "Memory", 
        order_by: Optional[str] = None, 
        query_id: Optional[str] = None
    ):
        """
        Initialize the temporary table context manager.
        
        Args:
            connection: Database connection
            processor: Query processor instance
            table_name: Name for the temporary table
            query: SQL query to populate the table
            engine: ClickHouse engine to use (default: Memory)
            order_by: Optional ORDER BY clause
            query_id: Optional custom query ID to use for tracking
        """
        self.connection = connection
        self.processor = processor
        self.table_name = table_name
        self.query = query
        self.engine = engine
        self.order_by = order_by
        self.query_id = query_id
        self.name = None
        self.logger = processor.logger
        
    def __enter__(self):
        """Create the temporary table when entering the context."""
        try:
            self.logger.info(f"Creating temporary table '{self.table_name}'")
            
            # Create the temporary table with custom query ID if provided
            settings = {"query_id": self.query_id} if self.query_id else None
            self.name = self.connection.create_temp_table(
                self.query, self.table_name, self.engine, self.order_by, settings
            )
            
            # Track the table if created successfully
            self.processor.temp_tables.append(self.name)
            self.logger.info(f"Temporary table '{self.table_name}' created successfully")
            return self
            
        except Exception as e:
            # Use centralized error handling
            error = handle_query_error(e, f"Error creating temp table {self.table_name}", self.logger)
            raise error
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up the temporary table when exiting the context."""
        if self.name and self.name in self.processor.temp_tables:
            try:
                if self.connection.drop_temp_table(self.name):
                    self.processor.temp_tables.remove(self.name)
                    self.logger.info(f"Temporary table '{self.table_name}' dropped successfully")
                else:
                    self.logger.warning(f"Failed to drop temporary table '{self.table_name}'")
            except Exception as e:
                # Just log the error but don't propagate it
                self.logger.warning(f"Error dropping temporary table '{self.table_name}': {e}")
        return False  # Don't suppress exceptions
