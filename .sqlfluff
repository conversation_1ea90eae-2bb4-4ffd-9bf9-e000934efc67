[sqlfluff]
dialect = clickhouse
templater = jinja
exclude_rules = L016, L031, L034

[sqlfluff:indentation]
indented_joins = True
indented_using_on = True
template_blocks_indent = True
tab_space_size = 4

[sqlfluff:layout:type:comma]
line_position = trailing

[sqlfluff:templater:jinja]
apply_dbt_builtins = False

[sqlfluff:rules:capitalisation.keywords]
capitalisation_policy = consistent

[sqlfluff:rules:aliasing.table]
aliasing = explicit

[sqlfluff:rules:aliasing.column]
aliasing = explicit

[sqlfluff:rules:aliasing.expression]
allow_scalar = True

[sqlfluff:rules:capitalisation.identifiers]
extended_capitalisation_policy = consistent

[sqlfluff:rules:capitalisation.functions]
capitalisation_policy = consistent

[sqlfluff:rules:capitalisation.literals]
capitalisation_policy = consistent

[sqlfluff:rules:ambiguous.column_references]
group_by_and_order_by_style = consistent
