"""
ClickHouse error parser utility.

This module provides utilities for parsing ClickHouse error messages
and extracting error codes and other information.
"""

import re
from typing import Op<PERSON>, Dict, Any


def parse_clickhouse_error(error_message: str) -> Dict[str, Any]:
    """
    Parse a ClickHouse error message to extract error code and type.

    Args:
        error_message: The error message from ClickHouse

    Returns:
        Dictionary with error_code, error_type, and original_message
    """
    result = {"error_code": None, "error_type": None, "original_message": error_message}

    # Pattern to match ClickHouse error codes like "Code: 62." or "Code: 62. DB::Exception"
    code_pattern = r"Code: (\d+)[.\s]"
    code_match = re.search(code_pattern, error_message)

    if code_match:
        result["error_code"] = int(code_match.group(1))

    # Pattern to match ClickHouse error types like "DB::Exception:"
    type_pattern = r"(DB::\w+Exception):"
    type_match = re.search(type_pattern, error_message)

    if type_match:
        result["error_type"] = type_match.group(1)

    return result


def is_clickhouse_error(error_message: str) -> bool:
    """
    Check if an error message is from ClickHouse.

    Args:
        error_message: The error message to check

    Returns:
        True if the error is from ClickHouse, False otherwise
    """
    # Check for common ClickHouse error patterns
    clickhouse_patterns = [
        r"Code: \d+[.\s]",
        r"DB::\w+Exception:",
        r"ClickHouse\s+error",
        r"clickhouse_connect\.",
        r"error code \d+",
    ]

    for pattern in clickhouse_patterns:
        if re.search(pattern, error_message, re.IGNORECASE):
            return True

    return False


def get_simplified_error_message(
    error_message: str, error_code: Optional[int] = None
) -> str:
    """
    Extract a simplified error message from a ClickHouse error.

    Args:
        error_message: The full error message
        error_code: Optional error code to include in the message

    Returns:
        A simplified error message with just the essential information
    """
    # Extract the first line or sentence of the error message
    first_line = error_message.split("\n")[0].strip()

    # If the message is too long, truncate it
    if len(first_line) > 200:
        first_line = first_line[:197] + "..."

    # If we have an error code, make sure it's included in the simplified message
    if error_code:
        # Check if the error code is already in the message
        error_code_str = str(error_code)
        if (
            f"error code {error_code_str}" not in first_line.lower()
            and f"Code: {error_code_str}" not in first_line
            and not re.search(rf"\(Error code: {error_code_str}\)", first_line)
        ):
            return f"{first_line} (Code: {error_code_str})"

    return first_line
