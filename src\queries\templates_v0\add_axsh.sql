{% macro render_axis_join(axis_key, axis_data, start_pos=None, end_pos=None) %}
{% if axis_data["type"] == "axsh" %}
    INNER JOIN (
    {# Add CTE if available #}
    {% if axis_data["ddl"]["cte"] %}
        WITH {{ axis_data["ddl"]["cte"] }}
    {% endif %}

    {# Add queries with UNION ALL #}
    {% if start_pos is none and end_pos is none %}
        {% if axis_data["ddl"]["queries"]|length > 0 %}
            {{ axis_data["ddl"]["queries"][0] }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
            {% for query in axis_data["ddl"]["queries"][1:] %}
                UNION ALL
                {{ query }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
            {% endfor %}
        {% endif %}
        {% else %}
        {% if axis_data["ddl"]["queries"]|length > start_pos %}
            {{ axis_data["ddl"]["queries"][start_pos] }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
            {% if axis_data["ddl"]["queries"]|length > start_pos + 1 %}
                {% for query in axis_data["ddl"]["queries"][start_pos + 1:end_pos] %}
                    UNION ALL
                    {{ query }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
                {% endfor %}
            {% endif %}
        {% endif %}
    {% endif %}
    ) {{ axis_key }} USING (hhkey)
{% endif %}
{% endmacro %}


add_axsh AS (
SELECT
{% for axis_key, axis_data in axes.items() %}
    {% if axis_data["type"] is not none %}
        {% if axis_data["type"] == "axsh" %}
            position_number AS {{ axis_key }}_position_number,
            {% else %}
            {{ axis_key }}_position_number,
        {% endif %}
    {% endif %}
{% endfor %}
rwbasis,
SUM(buyers_in_shop_ww) AS buyers_ww,
COUNTIf(DISTINCT hhkey, buyers_in_shop_ww != 0) AS buyers_raw,
sum(trips_in_shop_ww) AS trips_ww,
sum(trips_in_shop_fm) AS trips_fm,
sum(trips_anywhere_fm) AS trips_anywhere_fm,
sum(value_buyers_in_shop) AS value_buyers_in_shop,
sum(value_buyers_anywhere) AS value_buyers_anywhere,
sum(value_buyers_elsewhere) AS value_buyers_elsewhere,
sum(buyers_anywhere_ww) AS buyers_anywhere_ww,
any(population) AS population,
any(projectc) as projectc
FROM buyers_final a
INNER JOIN (
WITH population AS (
SELECT
hhkey,
sum(fullmass) / (age('month', toDate('{{ period_start }}'), toDate('{{ period_end }}')) + 1) AS weight_wave
FROM pet.hh_weights_fullmass
WHERE (id_panel={{ id_panel }}) AND (dt_start >= '{{ period_start }}') AND (dt_end <= '{{ period_end }}')
GROUP BY hhkey
)
SELECT
hhkey,
sum(weight_wave)
{% if "axsh" in axes.values()|map(attribute="type") %}
    OVER (PARTITION BY position_number) AS population,
    position_number
    {% else %}
    OVER () AS population
{% endif %}
FROM population
{% if "axsh" in axes.values()|map(attribute="type") %}
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            {{ render_axis_join(axis_key, axis_data, start_pos, end_pos) }}
        {% endif %}
    {% endfor %}
{% endif %}
{% for filter_key, filter_data in filters.items() %}
    {% if filter_data["type"] == "flth" %}
        WHERE hhkey IN
        (SELECT hhkey FROM (
        {% if filter_data["ddl"]["cte"] %}
            WITH {{ filter_data["ddl"]["cte"] }}
        {% endif %}
        {% if filter_data["ddl"]["queries"]|length > 0 %}
            {{ filter_data["ddl"]["queries"][0] }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
            {% for query in filter_data["ddl"]["queries"][1:] %}
                UNION ALL
                {{ query }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
            {% endfor %}
        {% endif %}
        ))
    {% endif %}
{% endfor %}
)  p USING (hhkey)
{% for filter_key, filter_data in filters.items() %}
    {% if filter_data["type"] is not none and filter_data["type"] == "flth" %}
        {% if "axsh" in axes.values()|map(attribute="type") and start_pos is not none and end_pos is not none %} AND {% else %} WHERE {% endif %} hhkey IN
            (SELECT hhkey FROM (
            {% if filter_data["ddl"]["cte"] %}
                WITH {{ filter_data["ddl"]["cte"] }}
            {% endif %}
            {% if filter_data["ddl"]["queries"]|length > 0 %}
                {{ filter_data["ddl"]["queries"][0] }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
                {% for query in filter_data["ddl"]["queries"][1:] %}
                    UNION ALL
                    {{ query }} AND dt_record = toStartOfMonth(toDate('{{ period_end }}'))
                {% endfor %}
            {% endif %}
            ))
        {% endif %}
    {% endfor %}
    GROUP BY rwbasis
    {% for axis_key, axis_data in axes.items() %}
        {% if axis_data["type"] is not none %}
            , {{ axis_key }}_position_number
        {% endif %}
    {% endfor %}
    )
