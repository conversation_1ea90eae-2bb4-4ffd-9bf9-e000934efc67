[tool.poetry]
name = "catman"
version = "1.2.15"
description = ""
authors = ["Taho1 <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
clickhouse-driver = "^0.2.9"
python-dotenv = "^1.0.1"
sqlalchemy = "^2.0.36"
pandas = "^2.2.3"
openpyxl = "^3.1.5"
clickhouse-connect = "^0.8.13"
tabulate = "^0.9.0"
xlsxwriter = "^3.2.2"
python-dateutil = "^2.9.0.post0"
psycopg2-binary = "^2.9.10"
jinja2 = "^3.1.5"
pydantic = "^2.10.6"
magic-gateway-client = {git = "https://gitlab.icmr.ru/dev/mg_client.git"}
sqlfluff = "^3.4.0"
httpx = "^0.28.1"
dotenv = "^0.9.9"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-cov = "^4.1.0"
pytest-mock = "^3.12.0"
black = "^24.3.0"
isort = "^5.13.2"
mypy = "^1.8.0"
flake8 = "^7.0.0"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
markers = [
    "unit: marks tests as unit tests",
    "integration: marks tests as integration tests",
    "slow: marks tests as slow (skipped by default)",
]
addopts = "--strict-markers -v"

[tool.coverage.run]
source = ["src"]
omit = ["tests/*", "**/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
]

[tool.black]
line-length = 88
target-version = ["py312"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88
