"""
Axis service for the application.

This module provides a service for handling axis and filter data processing.
It encapsulates the functionality of the ObjectProcessor and ValidationProcessor classes.
"""

from typing import Dict, Optional, List, Callable, Any

from src.models.axis import AxisData, FilterData, FactsData
from src.models.kpi import KPIRequest, ValidationResult
from src.processors.object_processor import ObjectProcessor
from src.processors.validation_processor import ValidationProcessor
from src.services.base_service import BaseService
from src.utils.data_processing import calculate_split_parameters


class AxisService(BaseService):
    """
    Service for handling axis and filter data processing.

    This service encapsulates the functionality of the ObjectProcessor class
    and provides a more service-oriented interface.
    """

    def __init__(
        self,
        mg_api_url: Optional[str] = None,
        mg_api_user: Optional[str] = None,
        mg_api_password: Optional[str] = None,
        msg_logger_func: Optional[Callable] = None,
    ):
        """
        Initialize the axis service.

        Args:
            mg_api_url: Magic Gateway API URL
            mg_api_user: Magic Gateway API username
            mg_api_password: Magic Gateway API password
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)

        # Store API credentials for creating ObjectProcessor instances
        self.mg_api_url = mg_api_url
        self.mg_api_user = mg_api_user
        self.mg_api_password = mg_api_password

        # Create the validation processor
        self.validation_processor = ValidationProcessor()

    def get_all_data(
        self,
        axes: Dict[str, AxisData],
        filters: Dict[str, FilterData],
        id_panel: int,
        facts_data: Optional[List[FactsData]] = None,
    ) -> Dict[str, Any]:
        """
        Get all data (axes, filters, and measures) in a single call.

        This method initializes the object processor with all the required parameters
        and returns the populated data.

        Args:
            axes: Dictionary of axis key to AxisData model
            filters: Dictionary of filter key to FilterData model
            id_panel: Panel ID to use for table mapping
            facts_data: Optional list of FactsData models

        Returns:
            Dictionary containing axes_data, filters_data, and measures_data
        """
        self.log_message(
            f"Getting all data in a single call: {len(axes)} axes, {len(filters)} filters, {len(facts_data) if facts_data else 0} facts"
        )

        # Create a new object processor with all parameters
        object_processor = ObjectProcessor(
            axes=axes,
            filters=filters,
            id_panel=id_panel,
            facts_axis=facts_data,  # ObjectProcessor still uses facts_axis parameter name
            mg_api_url=self.mg_api_url,
            mg_api_user=self.mg_api_user,
            mg_api_password=self.mg_api_password,
        )

        return {
            "axes_data": object_processor.axes_data,
            "filters_data": object_processor.filters_data,
            "measures_data": object_processor.measures_data,
        }

    def validate_and_prepare_data(
        self,
        kpi_request: KPIRequest,
        dialect: str = "clickhouse",
    ) -> ValidationResult:
        """
        Validate and prepare all data for the query builder in a single call.

        This method centralizes all validation and preparation logic before passing
        data to the query builder. It ensures that all parameters are properly validated
        and populated with the necessary information.

        Args:
            kpi_request: KPIRequest model containing all request parameters
            dialect: Database dialect to use for the SU fact formula ("clickhouse" or "postgres")

        Returns:
            ValidationResult model containing validated and processed data for the query builder:
            - axes: Validated and processed axes with DDL information
            - filters: Validated and processed filters with DDL information
            - facts_data: Enriched facts_data with required data
            - required_facts: List of unique required facts extracted from facts_data
            - su_fact_data: SUFactData model with SU fact data if provided
            - periods: List of validated Period models
            - id_panel: Panel ID from the request
        """
        self.log_message(
            f"Validating and preparing data for {kpi_request.kpi_type.value}"
        )

        # First, validate all data using the validation processor
        validation_result: ValidationResult = self.validation_processor.validate_all(
            axes=kpi_request.axes,
            filters=kpi_request.filters,
            periods=kpi_request.periods,
            facts_data=kpi_request.facts_axis,
            su_fact=kpi_request.su_fact,
            id_panel=kpi_request.id_panel,
            dialect=dialect,
        )

        # Then, get all data in a single call to populate axes_data, filters_data, and measures_data
        all_data = self.get_all_data(
            axes=validation_result.axes,
            filters=validation_result.filters,
            id_panel=kpi_request.id_panel,
            facts_data=validation_result.facts_data,
        )

        # Get SU fact data from validation result
        su_fact_data = validation_result.su_fact_data
        if su_fact_data:
            self.log_message(f"SU fact data validated for {kpi_request.su_fact}")

        # Get measures data from all_data
        measures_data = all_data.get("measures_data", [])

        # Update the ValidationResult with processed data
        # Replace the axes and filters with their processed versions that include DDL information
        validation_result.axes = all_data["axes_data"]
        validation_result.filters = all_data["filters_data"]
        validation_result.facts_data = measures_data

        # Extract all unique required facts from the populated facts_data
        # This ensures we capture any required facts that might have been added during processing
        required_facts = self.validation_processor.extract_required_facts(measures_data)
        validation_result.required_facts = required_facts

        # Calculate split parameters for each period
        for period in validation_result.periods:
            (
                period.total_positions,
                period.split_axis,
                period.threshold,
            ) = calculate_split_parameters(
                (period.label, period.date_start, period.date_end),
                validation_result.axes,
                self.logger,
            )
            self.log_message(
                f"Period '{period.label}': total_positions={period.total_positions}, "
                f"split_axis='{period.split_axis}', threshold={period.threshold}"
            )

        self.log_message(
            f"Extracted {len(validation_result.required_facts)} unique required facts"
        )
        self.log_message(
            f"Processed {len(validation_result.axes)} axes and {len(validation_result.filters)} filters"
        )

        return validation_result
