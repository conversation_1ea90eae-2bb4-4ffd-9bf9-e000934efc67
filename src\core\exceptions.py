"""
Custom exceptions for the application.

This module defines custom exceptions for the application to provide
more specific error handling and better error messages.
"""


class KPIError(Exception):
    """Base exception for all KPI application errors."""

    pass


class ConfigurationError(KPIError):
    """Exception raised for errors in the configuration."""

    pass


class ConnectionError(KPIError):
    """Exception raised for errors in database connections."""

    pass


class QueryError(KPIError):
    """Exception raised for errors in SQL queries.

    Attributes:
        error_code: Optional ClickHouse error code
        error_type: Optional ClickHouse error type
        query: Optional query that caused the error
    """

    def __init__(self, message: str):
        super().__init__(message)
        self.error_code = None
        self.error_type = None
        self.query = None


class ProcessingError(KPIError):
    """Exception raised for errors in data processing."""

    pass


class ValidationError(KPIError):
    """Exception raised for validation errors."""

    pass


class ResourceError(KPIError):
    """Exception raised for errors related to system resources."""

    pass


class ExportError(KPIError):
    """Exception raised for errors in exporting data."""

    pass


class JobError(KPIError):
    """Exception raised for errors in job processing."""

    pass


class APIError(KPIError):
    """Exception raised for errors in API calls."""

    pass


class DataError(KPIError):
    """Exception raised for errors in data handling."""

    pass
