"""
Command-line utility to format SQL templates using SQLFluff.

This script formats all SQL templates in the project using SQLFluff.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.sql_template_manager import SQLTemplateManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def format_templates(
    template_dir: str = "./src/queries/templates",
    dialect: str = "clickhouse",
    extension: str = ".sql",
    dry_run: bool = False,
) -> None:
    """
    Format all SQL templates in the template directory.
    
    Args:
        template_dir: Directory containing SQL templates
        dialect: SQL dialect to use for formatting
        extension: File extension to filter by
        dry_run: If True, don't save changes, just report what would be changed
    """
    try:
        # Initialize template manager
        template_manager = SQLTemplateManager(template_dir=template_dir)
        
        # Get list of templates
        templates = template_manager.list_templates(extension=extension)
        
        if not templates:
            logger.info(f"No templates found in {template_dir} with extension {extension}")
            return
            
        logger.info(f"Found {len(templates)} templates to format")
        
        # Format each template
        success_count = 0
        failure_count = 0
        
        for template_name in templates:
            try:
                if dry_run:
                    # Just format without saving
                    formatted, success = template_manager.format_template(
                        template_name, dialect=dialect
                    )
                    if success:
                        logger.info(f"✅ Would format template: {template_name}")
                        success_count += 1
                    else:
                        logger.warning(f"❌ Would fail to format template: {template_name}")
                        failure_count += 1
                else:
                    # Format and save
                    success = template_manager.format_and_save_template(
                        template_name, dialect=dialect
                    )
                    if success:
                        logger.info(f"✅ Formatted template: {template_name}")
                        success_count += 1
                    else:
                        logger.warning(f"❌ Failed to format template: {template_name}")
                        failure_count += 1
                        
            except Exception as e:
                logger.error(f"Error formatting template {template_name}: {e}")
                failure_count += 1
                
        # Print summary
        logger.info(f"Formatting complete: {success_count} succeeded, {failure_count} failed")
        
    except Exception as e:
        logger.error(f"Error formatting templates: {e}")
        sys.exit(1)


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Format SQL templates using SQLFluff")
    
    parser.add_argument(
        "--template-dir",
        type=str,
        default="./src/queries/templates",
        help="Directory containing SQL templates",
    )
    
    parser.add_argument(
        "--dialect",
        type=str,
        default="clickhouse",
        help="SQL dialect to use for formatting",
    )
    
    parser.add_argument(
        "--extension",
        type=str,
        default=".sql",
        help="File extension to filter by",
    )
    
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Don't save changes, just report what would be changed",
    )
    
    args = parser.parse_args()
    
    format_templates(
        template_dir=args.template_dir,
        dialect=args.dialect,
        extension=args.extension,
        dry_run=args.dry_run,
    )


if __name__ == "__main__":
    main()
