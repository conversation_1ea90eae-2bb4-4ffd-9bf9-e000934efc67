"""
Job service for the application.

This module provides a service for handling job processing.
"""

import datetime
import hashlib
import time
from typing import Dict, Optional, List, Callable, Any

from src.core.connection import <PERSON>lickHouseConnection
from src.core.connection_manager import connection_manager
from src.core.resource_check import ResourceCheck
from src.core.exceptions import QueryError
from src.models.kpi import KPIType, KPIRequest, KPIResult, ValidationResult
from src.models.axis import Period
from src.services.axis_service import AxisService
from src.services.query_service import QueryService
from src.services.base_service import BaseService
from src.utils.job_api_client import get_job_request
from src.utils.formatting import format_duration
from src.utils.clickhouse_error_parser import (
    parse_clickhouse_error,
    get_simplified_error_message,
)
from src.core.config import config

DEFAULT_FACTS_AXIS_CATMAN = [
    {"id": 78, "relative_axis": None, "relative_position": None},  # Buyers in shop
    {"id": 79, "relative_axis": None, "relative_position": None},  # Value in Store
    {"id": 80, "relative_axis": None, "relative_position": None},  # Trips in Store
    {"id": 81, "relative_axis": None, "relative_position": None},  # Trips Potential
    {
        "id": 82,
        "relative_axis": None,
        "relative_position": None,
    },  # Value Potential (000 Rub)
    {
        "id": 83,
        "relative_axis": None,
        "relative_position": None,
    },  # Value share potential (Propensity)
    {
        "id": 84,
        "relative_axis": None,
        "relative_position": None,
    },  # Value Potential Exploitation
    {"id": 85, "relative_axis": None, "relative_position": None},  # Loyalty Base (000)
    {
        "id": 86,
        "relative_axis": None,
        "relative_position": None,
    },  # Shopper loyalty in store
    {
        "id": 87,
        "relative_axis": None,
        "relative_position": None,
    },  # Buyers Potential (000)
    {"id": 88, "relative_axis": None, "relative_position": None},  # Buyers closure rate
]

# Default facts_axis parameter as a dictionary where keys are fact IDs and values are property dictionaries
DEFAULT_FACTS_AXIS = [
    {"id": 55, "relative_axis": None, "relative_position": None},  # Value
    {"id": 60, "relative_axis": None, "relative_position": None},  # Volume
    {"id": 61, "relative_axis": None, "relative_position": None},  # Packs
    {"id": 37, "relative_axis": None, "relative_position": None},  # Population
    {"id": 14, "relative_axis": None, "relative_position": None},  # Buyers
    {"id": 35, "relative_axis": None, "relative_position": None},  # Penetration
    {"id": 62, "relative_axis": None, "relative_position": None},  # Volume_Promo
    {"id": 63, "relative_axis": None, "relative_position": None},  # Packs_Promo
    {"id": 64, "relative_axis": None, "relative_position": None},  # Volume_Promo_Share
    {
        "id": 65,
        "relative_axis": None,
        "relative_position": None,
    },  # Volume_Promo_Share_Packs
    {"id": 46, "relative_axis": None, "relative_position": None},  # Spend per Trip
    {
        "id": 76,
        "relative_axis": None,
        "relative_position": None,
    },  # Volume per Trip Packs
    {"id": 2, "relative_axis": None, "relative_position": None},  # Pack_size
    {"id": 6, "relative_axis": None, "relative_position": None},  # Price per volume
    {"id": 8, "relative_axis": None, "relative_position": None},  # Price per volume
    {"id": 10, "relative_axis": None, "relative_position": None},  # Buyers raw
    {"id": 52, "relative_axis": None, "relative_position": None},  # Trips
    {"id": 22, "relative_axis": None, "relative_position": None},  # Frequency
    {"id": 43, "relative_axis": None, "relative_position": None},  # Spend per buyer
    {"id": 70, "relative_axis": None, "relative_position": None},  # Volume per buyer
    {"id": 71, "relative_axis": None, "relative_position": None},  # Packs per buyer
    {"id": 75, "relative_axis": None, "relative_position": None},  # Volume per trip
    {"id": 40, "relative_axis": None, "relative_position": None},  # Repeat Rate
    {"id": 41, "relative_axis": None, "relative_position": None},  # Repeaters
    {
        "id": 36,
        "relative_axis": None,
        "relative_position": None,
    },  # Penetration Repeaters
    {"id": 77, "relative_axis": None, "relative_position": None},  # Trial buyers
    {"id": 33, "relative_axis": "first_axis", "relative_position": 2},  # Loyalty Volume
]


class JobService(BaseService):
    """
    Service for handling job processing.

    This service encapsulates the functionality of the KPIProcessor class
    and provides a more service-oriented interface.
    """

    def __init__(
        self,
        connection: Optional[ClickHouseConnection] = None,
        axis_service: Optional[AxisService] = None,
        query_service: Optional[QueryService] = None,
        resource_check: Optional[ResourceCheck] = None,
        msg_logger_func: Optional[Callable] = None,
    ):
        """
        Initialize the job service.

        Args:
            connection: ClickHouse connection
            axis_service: Axis service
            query_service: Query service
            resource_check: Resource check
            msg_logger_func: Optional function for logging messages to the UI
        """
        super().__init__(msg_logger_func)

        # Get the connection from the connection manager if not provided
        self.connection = connection or connection_manager.get_clickhouse_connection()

        # Create the axis service if not provided
        self.axis_service = axis_service or AxisService(
            mg_api_url=config.mg_api_url,
            mg_api_user=config.mg_api_user,
            mg_api_password=config.mg_api_password,
            msg_logger_func=msg_logger_func,
        )

        # Create the query service if not provided
        self.query_service = query_service or QueryService(
            connection=self.connection,
            msg_logger_func=msg_logger_func,
        )

        # Create the resource check if not provided
        self.resource_check = resource_check or ResourceCheck(self.connection)

    def get_job_request(self, job_id: int) -> Dict[str, Any]:
        """
        Get the job request for the specified job ID.

        Args:
            job_id: Job ID

        Returns:
            Job request dictionary
        """
        self.log_message(f"Getting job request for job {job_id}")

        return get_job_request(job_id)

    def create_kpi_request(self, job_request_dict: Dict[str, Any]) -> KPIRequest:
        """
        Create a KPIRequest model from a job request dictionary.

        Args:
            job_request_dict: Job request dictionary

        Returns:
            KPIRequest model
        """
        self.log_message("Creating KPI request model")

        # If facts_axis is not in the request but we have default facts_axis, add it to the request
        if job_request_dict["facts_axis"] is None:
            self.logger.info("No facts_axis in request, using default facts_axis")
            # Use the default facts_axis dictionary directly
            job_request_dict["facts_axis"] = DEFAULT_FACTS_AXIS

        # For DEBIGUNG
        # if job_request_dict["kpi_type"] == "standard_kpi":
        #     job_request_dict["kpi_type"] = "catman_kpi"
        #     job_request_dict["facts_axis"] = DEFAULT_FACTS_AXIS_CATMAN

        # # Default catman kpis for now
        if job_request_dict["kpi_type"] == "catman_kpi":
            job_request_dict["facts_axis"] = DEFAULT_FACTS_AXIS_CATMAN

        # Create KPIRequest model directly from the job request dictionary
        return KPIRequest(**job_request_dict)

    def generate_combined_result_id(
        self,
        job_id: str,
        analysis_name: str,
        periods: List[Period],
        kpi_type: KPIType,
    ) -> str:
        """
        Generate a combined result ID for all periods.

        Args:
            job_id: Job ID
            analysis_name: Analysis name
            periods: List of Period model objects
            kpi_type: KPI type

        Returns:
            Combined result ID
        """
        # Create a unique ID based on the analysis name, job ID, and timestamp
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        period_names = "_".join([p.label for p in periods])
        hash_input = f"{analysis_name}_{period_names}_{kpi_type}_{timestamp}"
        hash_part = hashlib.md5(hash_input.encode()).hexdigest()[:8]  # Use shorter hash
        return f"job_{job_id}_{hash_part}"

    def process_job(self, job_id: int, username: str) -> KPIResult:
        """
        Process a KPI job.

        Args:
            job_id: Job ID
            username: Username (may be overridden by job metadata)

        Returns:
            KPIResult model
        """
        # Start timing for total execution
        start_time = time.time()

        # Get and parse job request
        self.log_message("Fetching job request data")
        job_request_dict = self.get_job_request(job_id)
        print(job_request_dict)

        # Validate job request
        if not job_request_dict:
            error_message = "KPI: job request is empty!"
            self.logger.error(error_message)
            return KPIResult(result_ids=[], errors=[error_message])

        # Get actual username from job metadata
        job_metadata = job_request_dict.get("_metadata", {})
        actual_username = job_metadata.get("user_name", username)
        if actual_username and actual_username != username:
            self.logger.info(
                f"Using actual username from job metadata: {actual_username} (was: {username})"
            )
            username = actual_username

        # Create KPIRequest model
        kpi_request = self.create_kpi_request(job_request_dict)

        # Initialize result summary
        result_summary = KPIResult(result_ids=[], errors=[])

        combined_result_id = None  # Initialize combined_result_id
        try:
            # Validate and prepare all data for the query builder in a single call
            self.log_message("Validating and preparing data for query builder")
            validation_result: ValidationResult = (
                self.axis_service.validate_and_prepare_data(
                    kpi_request=kpi_request,
                )
            )

            # Extract periods from the validation result for processing
            periods = validation_result.periods

            # Log the validation results
            self.log_message(
                f"Validated and prepared data for query builder: {len(validation_result.axes)} axes, {len(validation_result.filters)} filters, {len(validation_result.facts_data or [])} facts, {len(validation_result.required_facts)} required facts"
            )

            # Create query builder with validated data directly from ValidationResult model
            self.log_message("Creating query builder with validated data")
            query_builder = self.query_service.create_query_builder(
                validation_result=validation_result,
            )

            # Create query processor
            self.log_message("Creating query processor")
            query_processor = self.query_service.create_query_processor(query_builder)

            # Generate a combined result ID for all periods
            self.log_message("Generating result ID for data storage")
            combined_result_id = self.generate_combined_result_id(
                str(job_id), kpi_request.analysis_name, periods, kpi_request.kpi_type
            )
            # Add to result_ids - the final data table creation will be skipped if there are errors
            result_summary.result_ids.append(combined_result_id)

            # Process all periods
            for period in periods:
                # Log progress
                self.log_message(f"Processing period: {period.label}")

                # Process the period
                try:
                    # Get SU fact name from validation_result if available
                    # Get this out of period calculation!
                    su_fact_name = None
                    if validation_result.su_fact_data:
                        su_fact_name = validation_result.su_fact_data.su_name

                    # Check system resources before processing
                    self.log_message(
                        "Checking system resources before processing query..."
                    )
                    self.resource_check.wait_for_memory()

                    _, error_info = self.query_service.process_query(
                        query_processor=query_processor,
                        period=period,
                        kpi_type=kpi_request.kpi_type,
                        job_id=str(job_id),
                        analysis_name=kpi_request.analysis_name,
                        id_panel=validation_result.id_panel or 1,
                        combined_result_id=combined_result_id,
                        username=username,
                        su_fact_name=su_fact_name,
                    )

                    # If error_info is returned, add it to the result_summary errors
                    if error_info:
                        self.logger.error(
                            f"Error detected during query processing: {error_info}"
                        )
                        result_summary.add_error(
                            error=error_info.get("message", "Unknown error"),
                            error_code=error_info.get("error_code"),
                            period=error_info.get("period"),
                        )
                except QueryError as e:
                    # Handle ClickHouse errors specifically
                    error_msg = (
                        f"ClickHouse query failed for period {period.label}: {e}"
                    )
                    self.logger.error(error_msg, exc_info=True)

                    # Add simplified error information to result
                    error_code = getattr(e, "error_code", None)
                    simplified_message = get_simplified_error_message(
                        str(e), error_code
                    )
                    result_summary.add_error(
                        error=simplified_message,
                        error_code=error_code,
                        period=period.label,
                    )

                    # Stop processing on ClickHouse errors
                    self.logger.error(
                        f"Stopping KPI processing due to ClickHouse error (code: {getattr(e, 'error_code', 'unknown')})"
                    )
                    break

                except Exception as e:
                    # Handle other errors
                    error_msg = f"Failed to process period {period.label}: {e}"
                    self.logger.error(error_msg, exc_info=True)

                    # Check if it's a ClickHouse error that wasn't caught as QueryError
                    error_info = parse_clickhouse_error(str(e))
                    if error_info["error_code"] is not None:
                        # It's a ClickHouse error - use simplified message
                        simplified_message = get_simplified_error_message(
                            str(e), error_info["error_code"]
                        )
                        result_summary.add_error(
                            error=simplified_message,
                            error_code=error_info["error_code"],
                            period=period.label,
                        )
                        # Stop processing
                        self.logger.error(
                            f"Stopping KPI processing due to ClickHouse error (code: {error_info['error_code']})"
                        )
                        break
                    else:
                        # Regular error, add to errors and continue
                        simplified_message = get_simplified_error_message(str(e))
                        result_summary.add_error(
                            error=simplified_message, period=period.label
                        )
                        # Continue with next period

            # Log completion
            self.log_message("Done")

        except QueryError as e:
            # Handle ClickHouse errors specifically
            error_msg = (
                f"Critical failure in KPI processing due to ClickHouse error: {e}"
            )
            self.logger.error(error_msg, exc_info=True)

            # Add simplified error information to result
            error_code = getattr(e, "error_code", None)
            simplified_message = get_simplified_error_message(str(e), error_code)
            result_summary.add_error(
                error=simplified_message,
                error_code=error_code,
            )

        except Exception as e:
            # Check if it's a ClickHouse error that wasn't caught as QueryError
            error_info = parse_clickhouse_error(str(e))
            if error_info["error_code"] is not None:
                # It's a ClickHouse error
                error_msg = f"Critical failure in KPI processing due to ClickHouse error (code: {error_info['error_code']}): {e}"
                self.logger.error(error_msg, exc_info=True)

                # Add simplified error information to result
                simplified_message = get_simplified_error_message(
                    str(e), error_info["error_code"]
                )
                result_summary.add_error(
                    error=simplified_message,
                    error_code=error_info["error_code"],
                )
            else:
                # Regular error
                error_msg = f"Critical failure in KPI processing: {e}"
                self.logger.error(error_msg, exc_info=True)
                simplified_message = get_simplified_error_message(str(e))
                result_summary.add_error(error=simplified_message)

        # Log the result status
        if not result_summary.errors:
            self.logger.info(f"Successfully processed result: {combined_result_id}")
        else:
            error_count = len(result_summary.errors)
            # Check if we have result_ids even though there were errors
            if result_summary.result_ids:
                self.logger.error(
                    f"Job completed with {error_count} error(s) - results stored in ClickHouse with ID(s): {', '.join(result_summary.result_ids)}"
                )
                self.logger.warning(
                    "Final result table was not created due to errors, but error records were stored in the results table"
                )
            else:
                self.logger.error(
                    f"Job failed with {error_count} error(s) and no results were stored in ClickHouse"
                )
                self.logger.error(
                    "Critical failure: No records were created in the results table"
                )

        # Calculate and log total execution time
        duration = format_duration((time.time() - start_time) * 1000)
        self.log_message(f"Total execution time: {duration}")

        return result_summary
