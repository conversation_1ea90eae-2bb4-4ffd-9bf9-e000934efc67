"""
Base service class for the application.

This module provides a base class for all services in the application.
It includes common functionality such as logging and configuration.
"""

import logging
from typing import Optional, Callable

from src.core.config import config


class BaseService:
    """
    Base class for all services in the application.

    This class provides common functionality such as logging and configuration.
    All service classes should inherit from this class.
    """

    def __init__(self, msg_logger_func: Optional[Callable] = None):
        """
        Initialize the base service.

        Args:
            msg_logger_func: Optional function for logging messages to the UI
        """
        # Initialize logger with the class name
        self.logger = logging.getLogger(self.__class__.__name__)

        # Store the message logger function
        self.msg_logger_func = msg_logger_func

    def log_message(self, message: str) -> None:
        """
        Log a message to both the logger and the UI if a message logger function is provided.

        Args:
            message: The message to log
        """
        # Always log to the class logger first
        self.logger.info(message)

        # If a message logger function is provided, use it as well
        if self.msg_logger_func:
            self.msg_logger_func(f"[{self.__class__.__name__}] {message}")
