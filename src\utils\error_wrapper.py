"""
Error wrapper utilities for the KPI application.

This module provides a completely different approach to error handling
that avoids exception chaining and the "direct cause" messages.
"""

import logging
import traceback
from typing import Any, Dict, Optional, Tuple, Union, List

from src.core.exceptions import QueryError
from src.utils.clickhouse_error_parser import (
    parse_clickhouse_error,
    get_simplified_error_message,
)


class ErrorInfo:
    """
    Container for error information that doesn't use exception chaining.

    This class captures all relevant information about an error without
    using Python's exception chaining mechanism, avoiding the
    "The above exception was the direct cause of the following exception"
    messages.
    """

    def __init__(
        self,
        message: str,
        error_code: Optional[int] = None,
        error_type: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        context: Optional[str] = None,
    ):
        """
        Initialize the error information container.

        Args:
            message: The error message
            error_code: Optional error code (e.g., ClickHouse error code)
            error_type: Optional error type (e.g., ClickHouse error type)
            original_exception: Optional original exception that was caught
            context: Optional context message describing where the error occurred
        """
        self.message = message
        self.error_code = error_code
        self.error_type = error_type
        self.original_exception = original_exception
        self.context = context
        self.traceback = traceback.format_exc() if original_exception else None

    def __str__(self):
        """Return a string representation of the error."""
        parts = []

        if self.context:
            parts.append(f"{self.context}: ")

        parts.append(self.message)

        if self.error_code:
            parts.append(f" (Error code: {self.error_code}")
            if self.error_type:
                parts.append(f", type: {self.error_type}")
            parts.append(")")

        return "".join(parts)

    def to_query_error(self) -> QueryError:
        """Convert the error information to a QueryError."""
        error = QueryError(str(self))
        error.error_code = self.error_code
        error.error_type = self.error_type
        return error

    def to_dict(self) -> Dict[str, Any]:
        """Convert the error information to a dictionary for storage."""
        result = {
            "message": self.message,
        }

        if self.error_code is not None:
            result["error_code"] = self.error_code

        if self.error_type:
            result["error_type"] = self.error_type

        return result


def capture_error(
    exception: Exception, context: str, logger: logging.Logger
) -> ErrorInfo:
    """
    Capture error information without using exception chaining.

    Args:
        exception: The exception that was caught
        context: A message providing context for the error
        logger: Logger instance

    Returns:
        An ErrorInfo object containing all relevant error information
    """
    # Log the error with the original traceback
    logger.error(f"{context}: {exception}", exc_info=True)

    # Check if it's already a QueryError
    if isinstance(exception, QueryError):
        return ErrorInfo(
            message=str(exception),
            error_code=getattr(exception, "error_code", None),
            error_type=getattr(exception, "error_type", None),
            original_exception=exception,
            context=context,
        )

    # Check if it's a ClickHouse error
    error_info = parse_clickhouse_error(str(exception))
    if error_info["error_code"] is not None:
        return ErrorInfo(
            message=str(exception),
            error_code=error_info["error_code"],
            error_type=error_info["error_type"],
            original_exception=exception,
            context=context,
        )

    # Regular error
    return ErrorInfo(
        message=str(exception), original_exception=exception, context=context
    )


def store_error(
    error_info: ErrorInfo,
    job_id: str,
    period: Union[Tuple[str, str, str], str],
    result_store: Any,
    logger: logging.Logger,
    query_steps: Optional[List[str]] = None,
    query_ids: Optional[List[str]] = None,
    job_duration_ms: Optional[float] = None,
    **context,
) -> None:
    """
    Store error information in the result store.

    Args:
        error_info: The ErrorInfo object containing error details
        job_id: The ID of the job
        period: The period being processed
        result_store: The result store instance
        logger: Logger instance
        query_steps: Optional list of query steps executed before the error
        query_ids: Optional list of query IDs used
        job_duration_ms: Optional job duration in milliseconds
        **context: Additional context information for result storage
    """
    # Create simplified message
    simplified_message = get_simplified_error_message(
        error_info.message, error_info.error_code
    )

    # Create error info dictionary for storage
    error_dict = {
        "message": simplified_message,
        "period": period[0] if isinstance(period, tuple) else period,
    }

    # Add error code and type if available
    if error_info.error_code is not None:
        error_dict["error_code"] = error_info.error_code

    if error_info.error_type:
        error_dict["error_type"] = error_info.error_type

    # Store the error
    try:
        result_store.store_result_direct(
            query_text="SELECT 'Error' AS error",  # Dummy query
            job_id=job_id,
            period=period,
            query_steps="\n\n".join(query_steps) if query_steps else "",
            query_ids=query_ids,
            job_duration=job_duration_ms,
            error_info=error_dict,
            **context,
        )
        logger.info(f"Error information stored in ClickHouse for job {job_id}")
    except Exception as store_error:
        logger.error(f"Failed to store error information: {store_error}")


class SafeTemporaryTable:
    """
    Context manager for temporary tables with improved error handling.

    This version uses the new error handling approach that avoids
    exception chaining.
    """

    def __init__(
        self,
        connection: Any,
        processor: Any,
        table_name: str,
        query: str,
        engine: str = "Memory",
        order_by: Optional[str] = None,
        query_id: Optional[str] = None,
    ):
        """
        Initialize the temporary table context manager.

        Args:
            connection: Database connection
            processor: Query processor instance
            table_name: Name for the temporary table
            query: SQL query to populate the table
            engine: ClickHouse engine to use (default: Memory)
            order_by: Optional ORDER BY clause
            query_id: Optional custom query ID to use for tracking
        """
        self.connection = connection
        self.processor = processor
        self.table_name = table_name
        self.query = query
        self.engine = engine
        self.order_by = order_by
        self.query_id = query_id
        self.name = None
        self.logger = processor.logger

    def __enter__(self):
        """Create the temporary table when entering the context."""
        if not self.query:
            self.logger.error("Query cannot be empty")
            raise ValueError("Query cannot be empty")

        if not self.table_name:
            self.logger.error("Table name cannot be empty")
            raise ValueError("Table name cannot be empty")

        # Use a completely different approach to avoid exception chaining
        # Don't use try-except at all, which prevents the "During handling" messages

        # Log the operation
        self.logger.info(f"Creating temporary table '{self.table_name}'")

        # Create the temporary table with custom query ID if provided
        settings = {"query_id": self.query_id} if self.query_id else None

        # Instead of catching exceptions, we'll use a direct approach
        # that completely avoids exception chaining
        result = self._create_table_safely(settings)

        if isinstance(result, Exception):
            # If we got an exception, convert it to an error message and raise a new exception
            error_msg = f"Error creating temp table {self.table_name}: {result}"

            # Extract error code if available
            error_code = None
            error_message = str(result)

            # First check if the error object has an error_code attribute
            if hasattr(result, "error_code"):
                error_code = result.error_code
            else:
                # Try to extract error code from the message using different patterns
                import re

                # Pattern 1: "error code 62"
                match = re.search(r"error code (\d+)", error_message.lower())
                if match:
                    error_code = int(match.group(1))
                else:
                    # Pattern 2: "Code: 62."
                    match = re.search(r"Code: (\d+)[.\s]", error_message)
                    if match:
                        error_code = int(match.group(1))

            # Log the extracted error code
            if error_code:
                self.logger.info(f"Extracted ClickHouse error code: {error_code}")

            # Create a new QueryError with the error message
            from src.core.exceptions import QueryError

            query_error = QueryError(error_msg)
            query_error.error_code = error_code

            # Raise the new exception without any chaining
            raise query_error

        # If we got here, the table was created successfully
        self.name = result
        self.processor.temp_tables.append(self.name)
        self.logger.info(f"Temporary table '{self.table_name}' created successfully")
        return self

    def _create_table_safely(self, settings):
        """
        Create a table without raising exceptions.

        Args:
            settings: Settings to pass to the create_temp_table method

        Returns:
            Either the table name if successful, or the exception if failed
        """
        # Call the create_temp_table method, which now returns either
        # the table name or a tuple ("ERROR", QueryError)
        result = self.connection.create_temp_table(
            self.query, self.table_name, self.engine, self.order_by, settings
        )

        # Check if the result is a tuple indicating an error
        if isinstance(result, tuple) and len(result) == 2 and result[0] == "ERROR":
            # Return the QueryError object
            return result[1]

        # Otherwise, return the table name
        return result

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Clean up the temporary table when exiting the context."""
        if self.name and self.name in self.processor.temp_tables:
            try:
                if self.connection.drop_temp_table(self.name):
                    self.processor.temp_tables.remove(self.name)
                    self.logger.info(
                        f"Temporary table '{self.table_name}' dropped successfully"
                    )
                else:
                    self.logger.warning(
                        f"Failed to drop temporary table '{self.table_name}'"
                    )
            except Exception as e:
                # Just log the error but don't propagate it
                self.logger.warning(
                    f"Error dropping temporary table '{self.table_name}': {e}"
                )
        return False  # Don't suppress exceptions
